from django.http import JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import Camera
from .forms import CameraForm

@login_required
def index(request):
    cameras = Camera.objects.filter(user=request.user)
    return render(request, 'cameras/index.html', {'cameras': cameras})

@login_required
def add_camera(request):
    if request.method == 'POST':
        form = CameraForm(request.POST)
        if form.is_valid():
            camera = form.save(commit=False)
            camera.user = request.user
            camera.save()
            return redirect('cameras:index')
    else:
        form = CameraForm()
    return render(request, 'cameras/add_camera.html', {'form': form})

@login_required
def update_camera(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    if request.method == 'POST':
        form = CameraForm(request.POST, instance=camera)
        if form.is_valid():
            form.save()
            return redirect('cameras:index')
    else:
        form = CameraForm(instance=camera)
    return render(request, 'cameras/update_camera.html', {'form': form})

@login_required
def delete_camera(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    if request.method == 'POST':
        camera.delete()
        return redirect('cameras:index')
    return render(request, 'cameras/confirm_delete.html', {'camera': camera})

@login_required
def camera_detail(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    return render(request, 'cameras/camera_detail.html', {'camera': camera})

@login_required
def check_camera_status(request):
    """Return the real-time streaming status of all cameras for this user"""
    from videostream.views import process_camera_manager
    import time
    cameras = Camera.objects.filter(user=request.user)
    statuses = {}

    for camera in cameras:
        camera_id = camera.id

        # Get real process status from ProcessCameraManager
        process_status = process_camera_manager.get_camera_status(camera_id)

        if process_status and process_status['is_alive']:
            # Process is alive, check for errors first
            if process_status['error_message']:
                status = 'error'
            elif process_status['last_heartbeat']:
                # Has received frames - this is the key check!
                current_time = time.time()
                heartbeat_age = current_time - process_status['last_heartbeat']

                if heartbeat_age < 30:  # Recent frame (within 30 seconds)
                    status = 'streaming'
                else:
                    status = 'timeout'
            else:
                # Process alive but no frames yet (starting up)
                # THIS IS THE IMPORTANT PART - process is alive but no heartbeat = starting
                status = 'starting'
        else:
            # Process not alive
            status = 'not_streaming'

        statuses[str(camera_id)] = status

    return JsonResponse({'statuses': statuses})
