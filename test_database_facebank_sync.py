#!/usr/bin/env python3
"""
Test script to analyze synchronization between database and facebank.pth file
This will help understand what happens when a person is deleted from database
but still exists in facebank.pth or vice versa
"""

import os
import sys
import django
import torch
import numpy as np
from datetime import datetime

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
sys.path.append('./demo_server')
django.setup()

from alerts.models import AlertPerson, AlertPhoto
from recognition.facenet_recognizer import FaceNetRecognizer
from django.contrib.auth.models import User

def analyze_database_facebank_sync():
    """Analyze synchronization between database and facebank"""
    
    print("=" * 80)
    print("DATABASE vs FACEBANK SYNCHRONIZATION ANALYSIS")
    print("=" * 80)
    
    try:
        # Load facebank data
        facebank_dir = "./demo_server/media/alert_photos/"
        facebank_pth_path = os.path.join(facebank_dir, "facebank.pth")
        names_npy_path = os.path.join(facebank_dir, "names.npy")
        
        print(f"📁 Loading facebank from: {facebank_pth_path}")
        embeddings = torch.load(facebank_pth_path, map_location='cpu', weights_only=True)
        names = np.load(names_npy_path, allow_pickle=True)
        
        # Get unique names from facebank
        facebank_names = set(names)
        print(f"🔢 Facebank contains {len(facebank_names)} unique names")
        
        # Get names from database
        db_persons = AlertPerson.objects.all().values_list('name', flat=True)
        db_names = set(db_persons)
        print(f"🗄️  Database contains {len(db_names)} unique persons")
        
        # Find mismatches
        only_in_facebank = facebank_names - db_names
        only_in_database = db_names - facebank_names
        in_both = facebank_names & db_names
        
        print(f"\n📊 SYNCHRONIZATION STATUS:")
        print(f"   • In both DB and Facebank: {len(in_both)}")
        print(f"   • Only in Facebank (orphaned): {len(only_in_facebank)}")
        print(f"   • Only in Database (missing embeddings): {len(only_in_database)}")
        
        # Show orphaned entries (in facebank but not in database)
        if only_in_facebank:
            print(f"\n⚠️  ORPHANED ENTRIES IN FACEBANK (not in database):")
            for i, name in enumerate(sorted(only_in_facebank)[:20]):  # Show first 20
                count = list(names).count(name)
                print(f"   {i+1:2d}. {name} ({count} embeddings)")
            if len(only_in_facebank) > 20:
                print(f"   ... and {len(only_in_facebank) - 20} more orphaned entries")
        
        # Show missing embeddings (in database but not in facebank)
        if only_in_database:
            print(f"\n🚫 MISSING EMBEDDINGS (in database but not in facebank):")
            for i, name in enumerate(sorted(only_in_database)[:20]):  # Show first 20
                person = AlertPerson.objects.filter(name=name).first()
                photo_count = AlertPhoto.objects.filter(person=person).count() if person else 0
                print(f"   {i+1:2d}. {name} ({photo_count} photos in DB)")
            if len(only_in_database) > 20:
                print(f"   ... and {len(only_in_database) - 20} more missing embeddings")
        
        # Test what happens during recognition
        print(f"\n🤖 TESTING RECOGNITION BEHAVIOR:")
        
        # Initialize recognizer
        try:
            recognizer = FaceNetRecognizer()
            print(f"   ✅ FaceNet recognizer loaded successfully")
            print(f"   📊 Recognizer embeddings shape: {recognizer.embeddings.shape if recognizer.embeddings is not None else 'None'}")
            print(f"   📊 Recognizer names count: {len(recognizer.names) if recognizer.names is not None else 0}")
        except Exception as e:
            print(f"   ❌ Error loading recognizer: {e}")
            return
        
        # Test scenarios
        print(f"\n🧪 TESTING SCENARIOS:")
        
        # Scenario 1: Person exists in both
        if in_both:
            test_person = sorted(in_both)[0]
            print(f"   1️⃣ Person exists in both: '{test_person}'")
            print(f"      - This person should be recognized normally ✅")
        
        # Scenario 2: Person only in facebank (orphaned)
        if only_in_facebank:
            orphaned_person = sorted(only_in_facebank)[0]
            print(f"   2️⃣ Orphaned in facebank: '{orphaned_person}'")
            print(f"      - System can still recognize this person's face")
            print(f"      - But no database record exists for alerts/management")
            print(f"      - This creates inconsistency! ⚠️")
        
        # Scenario 3: Person only in database (missing embeddings)
        if only_in_database:
            missing_person = sorted(only_in_database)[0]
            print(f"   3️⃣ Missing embeddings: '{missing_person}'")
            print(f"      - System CANNOT recognize this person's face")
            print(f"      - Database record exists but no face model")
            print(f"      - Person will be treated as unknown! ❌")
        
        # Check for cleanup functions
        print(f"\n🧹 CLEANUP FUNCTIONS AVAILABLE:")
        try:
            from alerts.views import cleanup_orphaned_embeddings, verify_and_cleanup_model_files
            print(f"   ✅ cleanup_orphaned_embeddings() - Removes orphaned facebank entries")
            print(f"   ✅ verify_and_cleanup_model_files() - Verifies cleanup after deletion")
        except ImportError as e:
            print(f"   ❌ Cleanup functions not available: {e}")
        
        # Summary and recommendations
        print(f"\n🎯 CONCLUSIONS:")
        
        if len(only_in_facebank) > 0:
            print(f"   ⚠️  PROBLEM: {len(only_in_facebank)} orphaned entries in facebank")
            print(f"      - These persons can still be recognized by the system")
            print(f"      - But they don't exist in the database for management")
            print(f"      - Recommendation: Run cleanup_orphaned_embeddings()")
        
        if len(only_in_database) > 0:
            print(f"   ⚠️  PROBLEM: {len(only_in_database)} persons missing embeddings")
            print(f"      - These persons exist in database but cannot be recognized")
            print(f"      - They will be treated as 'unknown' during recognition")
            print(f"      - Recommendation: Re-add their photos or remove from database")
        
        if len(only_in_facebank) == 0 and len(only_in_database) == 0:
            print(f"   ✅ PERFECT SYNC: Database and facebank are perfectly synchronized!")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   - Always use the provided delete functions in alerts/views.py")
        print(f"   - These functions handle both database and facebank cleanup")
        print(f"   - Regular sync checks should be performed")
        
        return {
            'total_facebank': len(facebank_names),
            'total_database': len(db_names),
            'in_both': len(in_both),
            'orphaned': len(only_in_facebank),
            'missing_embeddings': len(only_in_database),
            'is_synchronized': len(only_in_facebank) == 0 and len(only_in_database) == 0
        }
        
    except Exception as e:
        print(f"❌ ERROR during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_deletion_scenario():
    """Test what happens when we simulate deletion scenarios"""
    
    print("\n" + "=" * 80)
    print("DELETION SCENARIO TESTING")
    print("=" * 80)
    
    # This is a simulation - we won't actually delete anything
    print("🧪 SIMULATING DELETION SCENARIOS:")
    
    print("\n📝 Scenario 1: Delete person from database only")
    print("   Result: Person embedding remains in facebank.pth")
    print("   Impact: System can still recognize the face")
    print("   Problem: No database record for alerts/management")
    print("   Status: ❌ INCONSISTENT STATE")
    
    print("\n📝 Scenario 2: Delete person from facebank only")
    print("   Result: Database record remains")
    print("   Impact: System cannot recognize the face")
    print("   Problem: Person treated as 'unknown'")
    print("   Status: ❌ INCONSISTENT STATE")
    
    print("\n📝 Scenario 3: Proper deletion (using system functions)")
    print("   Result: Both database and facebank updated")
    print("   Impact: Person completely removed from system")
    print("   Problem: None")
    print("   Status: ✅ CONSISTENT STATE")
    
    print("\n🔧 PROPER DELETION PROCESS:")
    print("   1. Delete from database (AlertPerson, AlertPhoto)")
    print("   2. Remove physical photo files")
    print("   3. Update facebank.pth (remove embeddings)")
    print("   4. Update names.npy (remove name entries)")
    print("   5. Verify cleanup completion")

if __name__ == "__main__":
    result = analyze_database_facebank_sync()
    test_deletion_scenario()
    
    if result:
        print(f"\n" + "=" * 80)
        print("FINAL SUMMARY")
        print("=" * 80)
        print(f"Database-Facebank Sync Status: {'✅ SYNCHRONIZED' if result['is_synchronized'] else '❌ NOT SYNCHRONIZED'}")
        print(f"Total inconsistencies: {result['orphaned'] + result['missing_embeddings']}")
        if not result['is_synchronized']:
            print(f"Action required: Run cleanup functions!")
