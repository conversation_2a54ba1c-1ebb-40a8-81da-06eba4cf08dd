#!/usr/bin/env python3
"""
Test script to check how many people are in the facebank.pth file
"""

import os
import sys
import torch
import numpy as np

def test_facebank_person_count():
    """Test function to count how many people are in the facebank"""
    
    # Path to facebank files
    facebank_dir = "./demo_server/media/alert_photos/facebank"
    facebank_pth_path = os.path.join(facebank_dir, "facebank.pth")
    names_npy_path = os.path.join(facebank_dir, "names.npy")
    
    print("=" * 60)
    print("FACEBANK PERSON COUNT TEST")
    print("=" * 60)
    
    # Check if files exist
    if not os.path.exists(facebank_pth_path):
        print(f"❌ ERROR: facebank.pth not found at {facebank_pth_path}")
        return
    
    if not os.path.exists(names_npy_path):
        print(f"❌ ERROR: names.npy not found at {names_npy_path}")
        return
    
    try:
        # Load the facebank embeddings
        print(f"📁 Loading facebank from: {facebank_pth_path}")
        embeddings = torch.load(facebank_pth_path, map_location='cpu', weights_only=True)
        
        # Load the names
        print(f"📁 Loading names from: {names_npy_path}")
        names = np.load(names_npy_path, allow_pickle=True)
        
        # Get dimensions and count
        print(f"\n📊 FACEBANK STATISTICS:")
        print(f"   • Embeddings shape: {embeddings.shape}")
        print(f"   • Number of names: {len(names)}")
        
        # Count unique people
        unique_names = list(set(names))
        unique_count = len(unique_names)
        
        print(f"   • Total face embeddings: {embeddings.shape[0]}")
        print(f"   • Total unique people: {unique_count}")
        print(f"   • Average embeddings per person: {embeddings.shape[0] / unique_count:.2f}")
        
        print(f"\n✅ RESULT: Facebank contains {unique_count} unique people")
        
        # Show some example names
        print(f"\n👥 Sample names (first 10):")
        for i, name in enumerate(sorted(unique_names)[:10]):
            count = list(names).count(name)
            print(f"   {i+1:2d}. {name} ({count} embeddings)")
        
        if unique_count > 10:
            print(f"   ... and {unique_count - 10} more people")
            
        print(f"\n📈 SUMMARY:")
        print(f"   Total People in Facebank: {unique_count}")
        print(f"   Total Face Embeddings: {embeddings.shape[0]}")
        print(f"   Embedding Dimension: {embeddings.shape[1]}")
        
    except Exception as e:
        print(f"❌ ERROR loading facebank: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_facebank_person_count()
