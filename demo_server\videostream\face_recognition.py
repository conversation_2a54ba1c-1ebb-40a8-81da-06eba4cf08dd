import os
import sys
import time
import uuid
from asyncio.log import logger
import aiofiles
import aiofiles.os
import torch
from django.conf import settings
from django.db import transaction
from django.core.files.base import ContentFile

from recognition.model_manager import ModelManager

# Django model'<PERSON><PERSON> import et - Django hazır olduğunda bu dosya yü<PERSON>ne<PERSON>k
from alerts.models import Alarm, AlertPerson, AlertPhoto
from cameras.models import Camera

import cv2
import numpy as np
from channels.layers import get_channel_layer
from asgiref.sync import sync_to_async
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta, datetime
from videostream.zone_utils import scale_zone_points, is_point_in_polygon

import os
from pathlib import Path
import threading

# Singleton FaceRecognitionProcessor sınıfı
class FaceRecognitionProcessor:
    _instance = None
    _init_lock = threading.Lock()
    
    def __new__(cls, camera_id=None):
        with cls._init_lock:
            if cls._instance is None:
                logger.info(f"Creating new FaceRecognitionProcessor instance")
                cls._instance = super(FaceRecognitionProcessor, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
        
    def __init__(self, camera_id=None):
        if camera_id is None:
            raise ValueError("camera_id is required for FaceRecognitionProcessor")
            
        # Eğer bu camera ID için daha önce init edilmişse, tekrar init etme
        if hasattr(self, '_initialized') and self._initialized:
            logger.info(f"FaceRecognitionProcessor already initialized")
            return
        
        # İlk kez init ediliyorsa tam init yap
        logger.info(f"Initializing FaceRecognitionProcessor for camera ID: {camera_id}")
        self.camera_id = camera_id
        self.recognizer = None
        
        # Model Manager'ı oluştur
        self.model_manager = ModelManager()
        
        # Tanıyıcıyı kameraya göre ayarla
        self._set_recognizer_for_camera()
        
        self.alert_cooldown = 60
        self.base_dir = settings.MEDIA_ROOT
        self.unknown_dir = os.path.join(self.base_dir, 'alert_photos', 'unknowns')
        self.known_dir = os.path.join(self.base_dir, 'alert_photos')
        self.unknown_count = 0

        # Ana dizini oluştur
        os.makedirs(self.unknown_dir, exist_ok=True)
        os.makedirs(self.known_dir, exist_ok=True)
        self._initialize_unknowns()
        
        self._initialized = True
        logger.info(f"FaceRecognitionProcessor successfully initialized for camera ID: {camera_id}")
    
    @classmethod
    def cleanup(cls):
        """Cleanup processor instance"""
        with cls._init_lock:
            if cls._instance is not None:
                cls._instance = None
                logger.info(f"Cleaned up FaceRecognitionProcessor instance")
    
    def _set_recognizer_for_camera(self):
        """Kamera ayarlarına göre doğru tanıyıcıyı belirle"""
        recognizer_type = "facenet"
        
        # ModelManager üzerinden modeli al
        self.recognizer = self.model_manager.get_model(recognizer_type)
        
        if self.recognizer:
            logger.info(f"Recognizer for camera {self.camera_id} is set to {type(self.recognizer).__name__}")
        else:
            logger.error(f"Could not set recognizer for camera {self.camera_id}")

    def _initialize_unknowns(self):
        """ 'unknowns' klasöründeki mevcut kişileri say """
        try:
            if self.recognizer.names is not None:
                numbers = []
                for name in self.recognizer.names:
                    if isinstance(name, str) and name.startswith("Unknown_"):
                        try:
                            num = int(name.split("_")[1])
                            numbers.append(num)
                        except (ValueError, IndexError):
                            continue

                self.unknown_count = max(numbers, default=0)
            return self.unknown_count
        except Exception as e:
            logger.error(f"Error initializing unknowns: {str(e)}")
            return 0

    def _handle_unknown_person(self, face_img, camera):
        """Handle unknown person detection and storage in AlertPerson table - Synchronous"""
        try:
            if face_img.size < 64 * 64 or face_img.size >= 64 * 64:
                self.unknown_count += 1
                person_name = f"Unknown_{self.unknown_count}"

                # Save temporary face.jpg for face recognition
                person_dir = os.path.join(self.unknown_dir, person_name)
                os.makedirs(person_dir, exist_ok=True)
                face_path = os.path.join(person_dir, "face.jpg")

                success = cv2.imwrite(face_path, face_img)

                if not success:
                    logger.error(f"Failed to save face image to {face_path}")
                    return None, None
                os.remove(face_path)
            try:
                camera_user = camera.user

                with transaction.atomic():
                    # Create AlertPerson
                    alert_person = AlertPerson.objects.create(
                        user=camera_user,
                        name=person_name,
                        is_unknown=True,
                        first_seen_camera=camera
                    )

                    # Create AlertPhoto and save face image
                    _, buffer = cv2.imencode('.jpg', face_img)
                    content = ContentFile(buffer.tobytes())
                    alert_photo = AlertPhoto.objects.create(
                        person=alert_person,
                        is_primary=True
                    )
                    alert_photo.photo.save(
                        f'{person_name}.jpg',
                        content,
                        save=True
                    )

                    # Dosya sistemine kaydet ve facebank'e ekle
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                    face_path = os.path.join(person_dir, f"face_{timestamp}.jpg")
                    success = cv2.imwrite(face_path, face_img)

                    if not success:
                        logger.error(f"Failed to save face image to {face_path}")
                        alert_person.delete()
                        return None, None

                    # Facebank'e ekle
                    result = self.recognizer.add_face(person_name, face_path)
                    if not result:
                        logger.error("Failed to add face to recognition system")
                        alert_person.delete()
                        if os.path.exists(face_path):
                            os.remove(face_path)
                        return None, None

                # Başarıyla eklendikten sonra face.jpg'yi sil
                os.remove(face_path)
                return alert_person, person_name

            except Exception as e:
                logger.error(f"Error creating unknown person records: {str(e)}")
                # Clean up database records
                if 'alert_person' in locals():
                    alert_person.delete()
                # Clean up face.jpg
                if os.path.exists(face_path):
                    os.remove(face_path)
                return None, None

        except Exception as e:
            logger.error(f"Error handling unknown person: {str(e)}")
            # Clean up temporary face.jpg
            if 'face_path' in locals() and os.path.exists(face_path):
                os.remove(face_path)
            return None, None

    def _check_existing_face(self, face_img):
        """Check if face exists - Synchronous"""
        try:
            if not isinstance(face_img, np.ndarray):
                return False, None

            bboxes, names, confidences = self.recognizer.recognize(
                face_img
            )

            if not isinstance(bboxes, np.ndarray) or bboxes.shape[0] == 0:
                return False, None

            name = names[0] if names else "Unknown"
            confidence = confidences[0] if isinstance(confidences, np.ndarray) and confidences.size > 0 else 0.0

            print(f"Face check result - Name: {name}, Confidence: {confidence}")

            if name != "Unknown":
                return True, name

            return False, None

        except Exception as e:
            logger.error(f"Error checking existing face: {str(e)}")
            return False, None

    # Yeni eklenen SENKRON yardımcı metod (ORM işlemleri burada)
    def _create_multiple_face_photo(self, frame, camera):
        """Senkron fotoğraf oluşturma metodu"""
        filename = f"snapshot_{uuid.uuid4()}.jpg"
        os.makedirs(os.path.join(settings.MEDIA_ROOT, 'alert_photos', 'multiple_faces'), exist_ok=True)

        # Frame'i işle
        _, buffer = cv2.imencode('.jpg', frame)
        content = ContentFile(buffer.tobytes())

        with transaction.atomic():
            alert_photo = AlertPhoto.objects.create(
                is_multiple_face=True,
                person=None  # Çoklu kişi için kişi ataması yok
            )
            alert_photo.photo.save(filename, content)

        logger.info(f"Multiple faces photo saved for camera {camera.name}")
        return alert_photo

    def is_face_in_zone(self, face_bbox, zone_points, video_width, video_height):
        """
        Check if a face is inside a polygon zone.
        
        Args:
            face_bbox: Face bounding box [x1, y1, x2, y2]
            zone_points: List of points defining the polygon zone [[x1,y1], [x2,y2], ...]
            video_width: Width of the video frame
            video_height: Height of the video frame
            
        Returns:
            bool: True if the face is inside the zone, False otherwise
        """
        try:
            # Calculate the bottom center point of the face bounding box
            x1, y1, x2, y2 = map(int, face_bbox[:4])
            bottom_center_x = (x1 + x2) // 2
            bottom_center_y = y2
            point = (bottom_center_x, bottom_center_y)
            
            # Scale zone points to match video dimensions
            scaled_polygon = scale_zone_points(zone_points, video_width, video_height)
            
            return is_point_in_polygon(point, scaled_polygon)
        except Exception as e:
            logger.error(f"Error checking if face is in zone: {str(e)}")
            return False

    async def process_frame(self, frame, camera, filter_by_zone=False, zones=None):
        """Process a frame with face recognition and unknown face handling - Asynchronous"""
        try:
            print(f"FACE_PROCESS_FRAME: camera_id={camera.id}, filter_by_zone={filter_by_zone}, zones_count={len(zones) if zones else 0}")
            
            bboxes, names, confidences = await sync_to_async(self.recognizer.recognize)(
                frame,
                camera_id=camera.id
            )

            if not isinstance(bboxes, np.ndarray) or bboxes.shape[0] == 0:
                print(f"FACE_PROCESS_FRAME: No faces detected")
                return frame

            print(f"FACE_PROCESS_FRAME: Detected {len(bboxes)} faces before filtering")
            
            frame_with_boxes = frame.copy()
            alert_photo = None  # Çoklu kişi fotoğrafı için
            
            # Filter faces by zone if zone filtering is active
            if filter_by_zone and zones:
                print(f"FACE_PROCESS_FRAME: Zone filtering ACTIVE - checking {len(bboxes)} faces against {len(zones)} zones")
                if len(zones) == 0:
                    print(f"FACE_PROCESS_FRAME: No zones defined, returning original frame")
                    return frame

                # Create filtered lists for faces within zones
                filtered_bboxes = []
                filtered_names = []
                filtered_confidences = []
                
                video_width = frame.shape[1]
                video_height = frame.shape[0]
                print(f"FACE_PROCESS_FRAME: Frame dimensions: {video_width}x{video_height}")

                for i, (bbox, name, confidence) in enumerate(zip(bboxes, names, confidences)):
                    face_in_any_zone = False
                    print(f"FACE_PROCESS_FRAME: Checking face {i} ({name}) with bbox: {bbox}")
                    for j, zone in enumerate(zones):
                        if self.is_face_in_zone(bbox, zone['points'], video_width, video_height):
                            print(f"FACE_PROCESS_FRAME: Face {i} IS IN zone {j} ({zone.get('name', 'unnamed')})")
                            face_in_any_zone = True
                            break
                        else:
                            print(f"FACE_PROCESS_FRAME: Face {i} NOT IN zone {j} ({zone.get('name', 'unnamed')})")
                    
                    if face_in_any_zone:
                        filtered_bboxes.append(bbox)
                        filtered_names.append(name)
                        filtered_confidences.append(confidence)
                        print(f"FACE_PROCESS_FRAME: Face {i} ADDED to filtered results")
                    else:
                        print(f"FACE_PROCESS_FRAME: Face {i} FILTERED OUT - not in any zone")
                
                # Replace original detections with filtered ones
                if len(filtered_bboxes) > 0:
                    bboxes = np.array(filtered_bboxes)
                    names = filtered_names
                    confidences = filtered_confidences
                    print(f"FACE_PROCESS_FRAME: After zone filtering: {len(bboxes)} faces remain")
                else:
                    print(f"FACE_PROCESS_FRAME: No faces in zones, returning original frame")
                    return frame
            else:
                print(f"FACE_PROCESS_FRAME: Zone filtering DISABLED - processing all {len(bboxes)} faces")

            # Birden fazla kişi tespit edildiğinde tek bir fotoğraf kaydet
            if len(names) > 1:
                # Camera bazlı cooldown key
                cache_key = f"multi_alert_cooldown_{camera.id}"
                last_alert_time = cache.get(cache_key)
                current_time = timezone.now()

                # Dakikada 1 kontrolü
                if not last_alert_time or (current_time - last_alert_time).total_seconds() >= 60:
                    # Senkron ORM işlemleri
                    create_photo = sync_to_async(self._create_multiple_face_photo, thread_sensitive=True)
                    alert_photo = await create_photo(frame, camera)
                    cache.set(cache_key, current_time, 60)  # 60 saniye cooldown

            for bbox, name, confidence in zip(bboxes, names, confidences):
                x1, y1, x2, y2 = map(int, bbox[:4])
                display_name = name

                face_img = frame
                if name == "Unknown":
                    exists, existing_name = await sync_to_async(self._check_existing_face)(face_img)

                    if exists and existing_name:
                        display_name = existing_name
                        logger.info(f"Matched with existing face: {existing_name}")
                    else:
                        alert_person, new_name = await sync_to_async(self._handle_unknown_person)(face_img, camera)
                        if alert_person and new_name:
                            display_name = new_name
                            logger.info(f"New unknown person saved to database: {new_name}")
                        else:
                            display_name = "Unknown"
                            logger.warning("Failed to save new unknown person")
                
                # Yüz çerçevesini çiz
                cv2.rectangle(frame_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)
                # Convert confidence to percentage for display
                confidence_percentage = confidence * 100
                cv2.putText(frame_with_boxes,
                            f"{display_name} ({confidence_percentage:.0f}%)",
                            (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX,
                            0.5,
                            (0, 255, 0),
                            2)

                if display_name != "Unknown" and not display_name.startswith("Unknown_"):
                    try:
                        person = await sync_to_async(AlertPerson.objects.filter(name=display_name).first)()
                        if person:
                            person.last_seen_date = timezone.now()
                            await sync_to_async(person.save)(update_fields=['last_seen_date'])
                            # handle_alert zaten async olduğu için sync_to_async kullanmıyoruz
                            # Alarmı oluştur (tek fotoğrafı tüm alarmlara bağla)
                            alert_result = await sync_to_async(self.handle_alert)(camera, display_name, confidence, frame, alert_photo)

                            if alert_result:
                                channel_layer = get_channel_layer()
                                await channel_layer.group_send(
                                    f"user_{alert_result['user_id']}",
                                    {
                                        "type": "send_alert",
                                        "message": f"Alert: {alert_result['name']} detected on camera {alert_result['camera_name']} with confidence {alert_result['confidence']:.2f}"
                                    }
                                )

                    except Exception as e:
                        logger.error(f"Error handling alert: {str(e)}")

            return frame_with_boxes

        except Exception as e:
            logger.error(f"Error in face recognition: {str(e)}")
            return frame

    def handle_alert(self, camera, name, confidence, frame, master_photo=None):
        try:
            if "Unknown" in name:
                return

            # Sync database query
            try:
                person = AlertPerson.objects.filter(name=name).first()
            except AlertPerson.DoesNotExist:
                logger.debug(f"No alert person found for name: {name}")
                return

            # Cache kontrolü
            cache_key = f"last_alert_{person.id}_{camera.id}"
            last_alert_time = cache.get(cache_key)

            current_time = timezone.now()

            if last_alert_time is None or (current_time - last_alert_time).total_seconds() >= self.alert_cooldown:
                # Alarm oluştur
                alarm = Alarm(
                    person=person,
                    camera=camera,
                    confidence=confidence,
                    alert_photo=master_photo # Tek fotoğrafı tüm alarmlara bağla
                )

                # Eğer çoklu kişi fotoğrafı varsa video_snapshot'i atla tek kişi ise kaydet
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if not master_photo:
                    # Tek kişi için yeni snapshot kaydet
                    _, buffer = cv2.imencode('.jpg', frame)
                    content = ContentFile(buffer.tobytes())
                    alarm.video_snapshot.save(
                        f'{person.name}_snapshot_{uuid.uuid4()}.jpg',
                        content
                    )

                # Save alarm
                alarm.save()


                # Cache'i güncelle
                cache.set(cache_key, current_time)

                # WebSocket bildirimi gönder - bunu async context'te yapacağız
                return {
                    "user_id": person.user.id,
                    "name": person.name,
                    "camera_name": camera.name,
                    "confidence": confidence
                }

        except Exception as e:
            logger.error(f"Error in handle_alert: {str(e)}", exc_info=True)
            return None