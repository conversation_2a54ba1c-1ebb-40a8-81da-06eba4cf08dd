import logging
import time
import cv2
import numpy as np
import asyncio
import traceback
import threading
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
from videostream.managers.commands import CameraCommand, CameraResult

# Django setup for multiprocessing
import os
import django
from django.conf import settings

# Do Django setup at the beginning of the process
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
    django.setup()

# Import after Django setup
from videostream.face_recognition import FaceRecognitionProcessor

logger = logging.getLogger(__name__)


def setup_tensorboard_writer(camera_id):
    """Setup TensorBoard writer for model delay logging"""
    try:
        # Create log directory with format: logs/camera_id/day/time
        now = datetime.now()
        day_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H-%M-%S")
        
        # Get project root directory
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        log_dir = os.path.join(project_root, "logs", f"camera_{camera_id}", day_str, time_str)
        
        # Ensure directory exists
        os.makedirs(log_dir, exist_ok=True)
        
        writer = SummaryWriter(log_dir=log_dir)
        logger.info(f"TensorBoard writer initialized for camera {camera_id} at {log_dir}")
        return writer
    except Exception as e:
        logger.error(f"Failed to initialize TensorBoard writer for camera {camera_id}: {str(e)}")
        return None


def log_frame_processing_time(writer, frame_processing_time, step):
    """Log frame processing time to TensorBoard"""
    if writer is not None:
        try:
            # Get current time for x-axis
            current_time = datetime.now()
            time_str = current_time.strftime("%H:%M:%S")
            
            # Log the delay with timestamp as scalar
            writer.add_scalar('Frame_Processing_Time/Processing_Time_Seconds', frame_processing_time, step)

            # Also add a text log with exact timestamp
            writer.add_text('Frame_Processing_Time/Timestamp', f"Time: {time_str}, Frame Processing Time: {frame_processing_time:.4f}s", step)

            # Flush to ensure data is written
            writer.flush()
        except Exception as e:
            logger.error(f"Error logging frame processing time to TensorBoard: {str(e)}")


def cleanup_tensorboard_writer(writer, camera_id):
    """Clean up TensorBoard writer"""
    if writer is not None:
        try:
            writer.close()
            logger.info(f"TensorBoard writer closed for camera {camera_id}")
        except Exception as e:
            logger.error(f"Error closing TensorBoard writer for camera {camera_id}: {e}")


def setup_worker_environment(camera_id):
    """Environment setup for worker process"""
    logger.info(f"Setting up environment for camera {camera_id}")
    
    # Asyncio event loop setup
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    return loop


def initialize_face_processor(camera_id, camera_obj):
    """Initialize face processor"""
    logger.info(f"INIT_FACE_PROCESSOR: camera_id={camera_id}, camera_obj={'None' if camera_obj is None else 'Present'}")
    print(f"INIT_FACE_PROCESSOR: camera_id={camera_id}, camera_obj={'None' if camera_obj is None else 'Present'}")
    
    if not camera_obj:
        logger.info(f"INIT_FACE_PROCESSOR: Camera object is None for camera {camera_id}, face recognition disabled - returning None")
        print(f"INIT_FACE_PROCESSOR: Camera object is None for camera {camera_id}, face recognition disabled - returning None")
        return None
    
    try:
        logger.info(f"INIT_FACE_PROCESSOR: Initializing FaceRecognitionProcessor for camera ID: {camera_id}")
        face_processor = FaceRecognitionProcessor(camera_id=camera_id)
        logger.info(f"INIT_FACE_PROCESSOR: Face processor successfully initialized for camera ID: {camera_id}")
        return face_processor
    except Exception as e:
        logger.error(f"INIT_FACE_PROCESSOR: Failed to initialize face processor for camera {camera_id}: {str(e)}")
        return None


def initialize_video_stream(camera_id, stream_url, result_queue):
    """Initialize video stream and wait for first real frame"""
    import time
    
    logger.info(f"Opening video stream: {stream_url}")
    compression_type = settings.STREAM_COMPRESSION

    try:
        if compression_type == "h264":
            stream = cv2.VideoCapture(stream_url, cv2.CAP_FFMPEG)
        elif compression_type == "mjpeg":
            stream = cv2.VideoCapture(stream_url)
        else:
            error_msg = f"Unsupported stream compression type: {compression_type}"
            logger.error(error_msg)
            result_queue.put({
                "type": CameraResult.ERROR,
                "message": error_msg,
                "camera_id": camera_id,
                "timestamp": time.time()
            })
            return None

        # Set buffer size to reduce latency
        stream.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # Wait for the first real frame (with timeout)
        max_attempts = 30  # 30 attempts = ~6 seconds with 0.2s intervals
        attempt = 0
        
        logger.info(f"Waiting for first frame from camera {camera_id}...")
        
        while attempt < max_attempts:
            ret, frame = stream.read()
            if ret and frame is not None and frame.size > 0:
                logger.info(f"Successfully received first frame from camera {camera_id} after {attempt + 1} attempts")
                # Send the first frame as heartbeat to signal process is truly ready
                result_queue.put({
                    "type": CameraResult.FRAME, #managerda buysa heartbeat set ediliyor aşadaki set fonk gönderiyor
                    "camera_id": camera_id,
                    "frame_data": frame,
                    "capture_time": time.time(),
                    "model_processed": False
                })
                return stream
            
            attempt += 1
            time.sleep(0.2)  # Wait 200ms between attempts
            
        # If we get here, no frame was received
        error_msg = f"No frame received from camera {camera_id} after {max_attempts} attempts (~6 seconds) - stream may be offline"
        logger.error(error_msg)
        result_queue.put({
            "type": CameraResult.ERROR,
            "message": error_msg,
            "camera_id": camera_id,
            "timestamp": time.time()
        })
        stream.release()
        return None
        
    except Exception as e:
        error_msg = f"Error initializing camera {camera_id}: {str(e)}"
        logger.error(error_msg)
        result_queue.put({
            "type": CameraResult.ERROR,
            "message": error_msg,
            "camera_id": camera_id,
            "timestamp": time.time()
        })
        return None


def get_command(command_queue, camera_id):
    """Get the next command from the command queue"""
    try:
        command_data = command_queue.get_nowait()
        command = command_data.get("command")
        if command:  # Only print if there's actually a command
            print(f"GET_COMMAND: camera_id={camera_id}, received command_data={command_data}")
            print(f"GET_COMMAND: camera_id={camera_id}, extracted command={command}")
        return command, command_data
    except Exception as e:
        # No command available - this is normal
        return None, None


def process_frame_with_model(frame, camera_obj, face_processor, loop, filter_by_zone, zones):
    """Process frame with model"""
    try:
        print(f"PROCESS_FRAME_WITH_MODEL: camera_obj={'Present' if camera_obj else 'None'}, filter_by_zone={filter_by_zone}, zones_count={len(zones) if zones else 0}")
        
        processing_start_time = time.time()

        processed_frame = loop.run_until_complete(
            face_processor.process_frame(
                frame,
                camera_obj,
                filter_by_zone=filter_by_zone,
                zones=zones
            )
        )
        
        processing_end_time = time.time()
        frame_processing_time = processing_end_time - processing_start_time

        return processed_frame, frame_processing_time

    except Exception as e:
        logger.error(f"Face recognition error: {str(e)}")
        return frame, 0.0  # Fallback to original frame


def send_frame_result(result_queue, camera_id, frame_data, capture_time, model_on):
    """Send processed frame to result queue"""
    try:
        result_queue.put_nowait({
            "type": CameraResult.FRAME,
            "camera_id": camera_id,
            "frame_data": frame_data,
            "capture_time": capture_time,
            "model_processed": model_on
        })
        return True
    except:
        return False  # Queue full
    
def close_loop(loop, camera_id, max_wait=5.0, interval=0.1):
    """Close the asyncio event loop gracefully"""
    waited = 0.0
    while loop.is_running() and waited < max_wait:
        print(f"Waiting for event loop to stop for camera {camera_id} ({waited:.1f}s)")
        time.sleep(interval)
        waited += interval

    try:
        if not loop.is_running():
            loop.close()
            logger.info(f"Event loop closed for camera {camera_id}")
        else:
            logger.error(f"Cannot close a running event loop for camera {camera_id} after waiting {max_wait}s")
    except Exception as e:
        logger.error(f"Error closing event loop for camera {camera_id}: {e}")


def cleanup_resources(stream, loop, camera_id, tensorboard_writer):
    """Clean up resources"""
    if stream is not None:
        try:
            stream.release()
            logger.info(f"Video stream released for camera {camera_id}")
        except Exception as e:
            logger.error(f"Error releasing video stream for camera {camera_id}: {e}")

    close_loop(loop, camera_id)
    cleanup_tensorboard_writer(tensorboard_writer, camera_id)



def safe_put(queue, item):
    """Safely put an item in the queue, ignoring if full"""
    if queue.full():
        try:
            _ = queue.get_nowait()  # Clear one item if full
        except Exception as e:
            logger.error(f"Error clearing queue: {e}")

    # Now try to put the item
    try:
        queue.put_nowait(item)
    except Exception as e:
        logger.error(f"Error putting item in queue: {e}")

def frame_resizer(frame, width=1920, height=1080):
    resized_frame = cv2.resize(frame, (width, height))
    return resized_frame

def handle_model_command(command, command_data, camera_id):
    """Handle model toggle command"""
    if command == CameraCommand.TOGGLE_MODEL.value:  # Compare with .value
        model_on = command_data.get("data", {}).get("model_on", True)
        logger.info(f"Camera {camera_id} model {'enabled' if model_on else 'disabled'}")
        return model_on
    return None

def handle_zone_update_command(command, command_data, camera_id):
    """Handle zone update command"""
    print(f"HANDLE_ZONE_UPDATE: checking command='{command}' (type: {type(command)}) vs CameraCommand.UPDATE_ZONES='{CameraCommand.UPDATE_ZONES.value}'")
    print(f"HANDLE_ZONE_UPDATE: command_data={command_data}")
    if command == CameraCommand.UPDATE_ZONES.value:  # Compare with .value
        zones = command_data.get("data", {}).get("zones", [])
        filter_by_zone = command_data.get("data", {}).get("filter_by_zone", False)
        print(f"HANDLE_ZONE_UPDATE: MATCH! camera_id={camera_id}, filter_by_zone={filter_by_zone}, zones_count={len(zones)}")
        logger.info(f"HANDLE_ZONE_UPDATE: camera_id={camera_id}, filter_by_zone={filter_by_zone}, zones_count={len(zones)}")
        if zones:
            for i, zone in enumerate(zones):
                print(f"HANDLE_ZONE_UPDATE: Zone {i}: {zone.get('name', 'unnamed')} - {len(zone.get('points', []))} points")
        return zones, filter_by_zone
    else:
        print(f"HANDLE_ZONE_UPDATE: NO MATCH, returning None")
    return None


def model_thread_function(camera_id, camera_obj, loop, stream_queue, command_queue, result_queue, shutdown_event, tensorboard_writer):
    logger.info(f"MODEL_THREAD: Starting model thread for camera {camera_id}")
    logger.info(f"MODEL_THREAD: camera_obj={'None' if camera_obj is None else 'Present'}")
    print(f"MODEL_THREAD: Starting model thread for camera {camera_id}")
    print(f"MODEL_THREAD: camera_obj={'None' if camera_obj is None else 'Present'}")
    
    face_processor = initialize_face_processor(camera_id, camera_obj)
    zones = []
    filter_by_zone = False
    model_on = True  # Default model state
    
    # TensorBoard writer setup
    step_counter = 0

    step_counter = 0  # Step counter for TensorBoard

    # If no face processor (zone creator mode), still process frames but without model
    if not face_processor:
        logger.info(f"MODEL_THREAD: No face processor initialized for camera {camera_id} - running in PASSTHROUGH MODE")
        print(f"MODEL_THREAD: No face processor initialized for camera {camera_id} - running in PASSTHROUGH MODE")
        
        # Passthrough mode - basit ve hızlı frame forwarding
        try:
            while True:
                if shutdown_event.is_set():
                    logger.info(f"Shutdown event received for camera {camera_id}, exiting model thread")
                    break
                if stream_queue.empty():
                    continue  # Wait for frames

                frame_data = stream_queue.get()
                frame = frame_data.get("frame")
                capture_time = frame_data.get("capture_time")

                if frame is None or capture_time is None:
                    continue

                # Direct passthrough - no processing
                send_frame_result(result_queue, camera_id, frame, capture_time, False)
        except Exception as e:
            logger.error(f"Error occurred in passthrough mode for camera {camera_id}: {e}")
        return
    else:
        logger.info(f"MODEL_THREAD: Face processor initialized for camera {camera_id} - running in FACE RECOGNITION MODE")
        print(f"MODEL_THREAD: Face processor initialized for camera {camera_id} - running in FACE RECOGNITION MODE")

    try:
        while True:
            if shutdown_event.is_set():
                cleanup_tensorboard_writer(tensorboard_writer, camera_id)
                logger.info(f"Shutdown event received for camera {camera_id}, exiting model thread")
                break
            
            # Always check for commands first
            command, command_data = get_command(command_queue, camera_id)
            if command:
                print(f"MODEL_THREAD: Processing command '{command}'")
                # Handle model commands
                model_result = handle_model_command(command, command_data, camera_id)
                if model_result is not None:
                    model_on = model_result
                
                # Handle zone commands  
                zone_info = handle_zone_update_command(command, command_data, camera_id)
                if zone_info:
                    zones, filter_by_zone = zone_info
                    print(f"MODEL_THREAD: Zone info updated - filter_by_zone={filter_by_zone}, zones_count={len(zones)}")
            
            # Process frames
            if stream_queue.empty():
                continue  # Wait for frames

            frame_data = stream_queue.get()
            frame = frame_data.get("frame")
            capture_time = frame_data.get("capture_time")

            if frame is None or capture_time is None:
                logger.error(f"Invalid frame data received for camera {camera_id}")
                continue

            # Process with model (if enabled and face_processor exists)
            if model_on and face_processor and camera_obj:
                print(f"MODEL_THREAD: Processing frame WITH face recognition - model_on={model_on}, filter_by_zone={filter_by_zone}")
                logger.debug(f"MODEL_THREAD: Processing frame WITH face recognition for camera {camera_id}")
                processed_frame, frame_processing_time = process_frame_with_model(
                    frame, camera_obj, face_processor, loop, filter_by_zone, zones
                )
                log_frame_processing_time(tensorboard_writer, frame_processing_time, step_counter)
                step_counter += 1
            else:
                logger.debug(f"MODEL_THREAD: Processing frame WITHOUT face recognition (passthrough) for camera {camera_id}")
                processed_frame = frame

            # Send processed frame to result queue
            send_frame_result(result_queue, camera_id, processed_frame, capture_time, model_on and face_processor is not None)
    except Exception as e:
        logger.error(f"Error occurred in model thread for camera {camera_id}: {e}")
        


def stream_thread_function(camera_id, stream_obj, command_queue, stream_queue, shutdown_event, resize=False, target_fps=2):
    if not stream_obj:
        return  # Stream initialization failed
    
    frame_interval = 1.0 / target_fps
    last_frame_time = time.time()

    while True:

        if shutdown_event.is_set():
            logger.info(f"Shutdown event received for camera {camera_id}, exiting stream thread")
            break

        if stream_obj is None or not stream_obj.isOpened():
            logger.error(f"Stream object is not opened for camera {camera_id}")
            shutdown_event.set()  # Signal shutdown to model thread
            break


        command, command_data = get_command(command_queue, camera_id)
        if command == CameraCommand.STOP:
            logger.info(f"Stop command received for camera {camera_id}")
            stream_obj.release()
            break #TODO: Handle stream stop command gracefully

        ret, frame = stream_obj.read()
        if not ret:
            logger.error(f"Failed to read frame from camera {camera_id}, stream may be disconnected")
            stream_obj.release()
            shutdown_event.set()  # Signal shutdown to model thread
            break  # Exit the loop to terminate the process

        current_time = time.time()
        if current_time - last_frame_time < frame_interval:
            continue  # FPS'i sabitlemek için bekle
        last_frame_time = current_time

        if resize:
            frame = frame_resizer(frame)

        capture_time = time.time()
        safe_put(stream_queue, {"frame": frame, "capture_time": capture_time})

    # Stream thread is ending, signal shutdown to model thread
    logger.info(f"Stream thread ending for camera {camera_id}, signaling shutdown")
    shutdown_event.set()

def camera_worker_process(camera_id, stream_url, stream_queue, result_queue, command_queue, shutdown_event, camera_obj):
    """
    Main Camera Worker Process
    Modular structure for better readability and maintainability

    Args:
        camera (CameraProcessInfo): Camera information object containing all necessary data.
        it is a copy of the real camera object. The change in the camera object does not affect
        the real camera object.
    """
    # Mark this as a worker process
    os.environ["IS_CAMERA_WORKER"] = "1"

    logger.info(f"Camera worker started for camera {camera_id}")
    loop = setup_worker_environment(camera_id)
    stream = initialize_video_stream(camera_id, stream_url, result_queue)
    tensorboard_writer = setup_tensorboard_writer(camera_id)

    # If stream initialization failed, exit immediately
    if stream is None:
        logger.error(f"Stream initialization failed for camera {camera_id}, exiting worker")
        cleanup_resources(stream, loop, camera_id, tensorboard_writer)
        return

    try:
        # Zone Creator (passthrough mode) için optimize edilmiş ayarlar
        if camera_obj is None:
            # Zone Creator: Yüksek FPS, resize yok
            target_fps = 15  # Daha akıcı görüntü için
            resize = False   # CPU tasarrufu için
            print(f"CAMERA_WORKER: Zone Creator mode - FPS={target_fps}, resize={resize}")
        else:
            # Stream Viewer: Normal ayarlar
            target_fps = 2   # Model processing için düşük FPS
            resize = False   # Resize'ı iptal ettik
            print(f"CAMERA_WORKER: Stream Viewer mode - FPS={target_fps}, resize={resize}")
        
        # Main processing loop
        stream_thread = threading.Thread(target=stream_thread_function, args=(camera_id, stream, command_queue, stream_queue, shutdown_event, resize, target_fps))
        model_thread = threading.Thread(target=model_thread_function, args=(camera_id, camera_obj, loop, stream_queue, command_queue, result_queue, shutdown_event, tensorboard_writer))
        stream_thread.start()
        model_thread.start()

        # Wait for threads to finish
        stream_thread.join()
        model_thread.join()

    except Exception as e:
        error_msg = f"Camera worker error for camera {camera_id}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        result_queue.put({
            "type": CameraResult.ERROR,
            "message": error_msg,
            "camera_id": camera_id,
            "timestamp": time.time()
        })

    finally:
        # Cleanup local resources
        cleanup_resources(stream, loop, camera_id, tensorboard_writer)
        logger.info(f"Camera worker ended for camera {camera_id}")

        # Notify manager about process termination
        try:
            result_queue.put({
                "type": CameraResult.PROCESS_TERMINATED,
                "camera_id": camera_id,
                "timestamp": time.time(),
                "reason": "normal_shutdown"
            })
        except Exception as e:
            logger.error(f"Error sending termination status for camera {camera_id}: {e}")
