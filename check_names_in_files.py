#!/usr/bin/env python3
"""
Simple script to check if target names exist in names.npy
and to verify that facebank.pth embeddings file is loadable.
"""

import os
import numpy as np
import torch

# <PERSON><PERSON><PERSON> yolları (senin workspace’e göre güncelle)
NAMES_FILE = "./demo_server/media/alert_photos/facebank/names.npy"
FACEBANK_FILE = "./demo_server/media/alert_photos/facebank/facebank.pth"

# Kontrol edilecek isimler
TARGET_NAMES = ["Kemal", "Unknown_5"]


def show_facebank_details(names, embeddings):
    """Facebank içeriğini detaylı göster"""
    print("\n" + "="*60)
    print("FACEBANK DETAYLI GÖRÜNÜM")
    print("="*60)
    
    if names is None and embeddings is None:
        print("❌ Facebank verileri yüklenemedi!")
        return
    
    if names is None:
        print("❌ İsimler yüklenemedi!")
        return
        
    if embeddings is None:
        print("❌ Embeddingler yüklenemedi!")
        return
    
    # Boyut kontrolü
    if len(names) != embeddings.shape[0]:
        print(f"⚠️  UYARI: İsim sayısı ({len(names)}) ve embedding sayısı ({embeddings.shape[0]}) eşleşmiyor!")
    
    print(f"📊 Toplam kişi sayısı: {len(names)}")
    print(f"📊 Embedding boyutu: {embeddings.shape}")
    print(f"📊 Her embedding'in boyutu: {embeddings.shape[1] if len(embeddings.shape) > 1 else 'N/A'}")
    
    print("\n📋 FACEBANK İÇERİĞİ:")
    print("-" * 40)
    
    for i, name in enumerate(names):
        if i < embeddings.shape[0]:
            embedding_norm = torch.norm(embeddings[i]).item()
            print(f"{i+1:2d}. {name:<20} | Embedding norm: {embedding_norm:.4f}")
        else:
            print(f"{i+1:2d}. {name:<20} | ❌ Embedding eksik!")
    
    # İstatistikler
    if embeddings.shape[0] > 0:
        avg_norm = torch.norm(embeddings, dim=1).mean().item()
        min_norm = torch.norm(embeddings, dim=1).min().item()
        max_norm = torch.norm(embeddings, dim=1).max().item()
        
        print(f"\n📈 EMBEDDING İSTATİSTİKLERİ:")
        print(f"   Ortalama norm: {avg_norm:.4f}")
        print(f"   Minimum norm:  {min_norm:.4f}")
        print(f"   Maksimum norm: {max_norm:.4f}")
    
    # Tekrarlanan isimler kontrolü
    unique_names = set(names)
    if len(unique_names) != len(names):
        print(f"\n⚠️  TEKRARLANAN İSİMLER BULUNDU:")
        name_counts = {}
        for name in names:
            name_counts[name] = name_counts.get(name, 0) + 1
        
        for name, count in name_counts.items():
            if count > 1:
                print(f"   {name}: {count} kez")


def check_facebank():
    # names.npy kontrolü
    if not os.path.exists(NAMES_FILE):
        print(f"✗ names.npy bulunamadı: {NAMES_FILE}")
        names = None
    else:
        names = np.load(NAMES_FILE, allow_pickle=True).tolist()
        print(f"names.npy yüklendi, toplam {len(names)} kişi var.")
        print("Kişiler:", names)

        for target in TARGET_NAMES:
            if target in names:
                print(f"✓ {target} bulundu.")
            else:
                print(f"✗ {target} bulunamadı.")

    # facebank.pth kontrolü
    embeddings = None
    if not os.path.exists(FACEBANK_FILE):
        print(f"\n✗ facebank.pth bulunamadı: {FACEBANK_FILE}")
    else:
        embeddings = torch.load(FACEBANK_FILE, map_location="cpu")
        if isinstance(embeddings, torch.Tensor):
            print(f"\nfacebank.pth yüklendi: Tensor shape = {embeddings.shape}")
        elif isinstance(embeddings, (list, tuple)):
            print(f"\nfacebank.pth yüklendi: {len(embeddings)} embedding içeriyor.")
        else:
            print(f"\nfacebank.pth içerik tipi: {type(embeddings)}")

    # Facebank içeriğini detaylı göster
    show_facebank_details(names, embeddings)


if __name__ == "__main__":
    check_facebank()
