#!/usr/bin/env python3
"""
Demo script to clean up orphaned entries in facebank.pth
This demonstrates what happens when database and facebank are out of sync
"""

import os
import sys
import django
import torch
import numpy as np
import shutil
from datetime import datetime

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
sys.path.append('./demo_server')
django.setup()

from alerts.models import <PERSON><PERSON><PERSON><PERSON>

def create_clean_facebank():
    """Create a clean facebank with only database persons"""
    
    print("=" * 80)
    print("FACEBANK CLEANUP DEMONSTRATION")
    print("=" * 80)
    
    # Get current data
    facebank_dir = "./demo_server/media/alert_photos/facebank"
    facebank_pth_path = os.path.join(facebank_dir, "facebank.pth")
    names_npy_path = os.path.join(facebank_dir, "names.npy")
    
    # Backup current files
    backup_dir = "./demo_server/media/alert_photos/facebank_backup"
    os.makedirs(backup_dir, exist_ok=True)
    
    backup_pth = os.path.join(backup_dir, f"facebank_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth")
    backup_npy = os.path.join(backup_dir, f"names_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.npy")
    
    print(f"💾 Creating backup:")
    print(f"   Original facebank.pth → {backup_pth}")
    print(f"   Original names.npy → {backup_npy}")
    
    shutil.copy2(facebank_pth_path, backup_pth)
    shutil.copy2(names_npy_path, backup_npy)
    
    # Load original data
    print(f"\n📁 Loading original facebank data...")
    embeddings = torch.load(facebank_pth_path, map_location='cpu', weights_only=True)
    names = np.load(names_npy_path, allow_pickle=True)
    
    print(f"   Original embeddings shape: {embeddings.shape}")
    print(f"   Original names count: {len(names)}")
    print(f"   Original unique names: {len(set(names))}")
    
    # Get database persons
    db_persons = list(AlertPerson.objects.all().values_list('name', flat=True))
    db_names_set = set(db_persons)
    
    print(f"\n🗄️  Database persons: {len(db_names_set)}")
    for name in sorted(db_names_set):
        print(f"   • {name}")
    
    # Find indices of persons that exist in database
    keep_indices = []
    keep_names = []
    
    for i, name in enumerate(names):
        if name in db_names_set:
            keep_indices.append(i)
            keep_names.append(name)
    
    print(f"\n🎯 Filtering facebank:")
    print(f"   Keeping {len(keep_indices)} embeddings out of {len(names)}")
    print(f"   Removing {len(names) - len(keep_indices)} orphaned embeddings")
    
    if keep_indices:
        # Create filtered data
        filtered_embeddings = embeddings[keep_indices]
        filtered_names = np.array(keep_names)
        
        print(f"\n✨ New facebank:")
        print(f"   New embeddings shape: {filtered_embeddings.shape}")
        print(f"   New names count: {len(filtered_names)}")
        print(f"   New unique names: {len(set(filtered_names))}")
        
        # Save filtered data
        print(f"\n💾 Saving cleaned facebank...")
        torch.save(filtered_embeddings, facebank_pth_path)
        np.save(names_npy_path, filtered_names)
        
        print(f"   ✅ Saved cleaned facebank.pth")
        print(f"   ✅ Saved cleaned names.npy")
        
        # Verify saved data
        print(f"\n🔍 Verifying saved data...")
        test_embeddings = torch.load(facebank_pth_path, map_location='cpu', weights_only=True)
        test_names = np.load(names_npy_path, allow_pickle=True)
        
        print(f"   Verified embeddings shape: {test_embeddings.shape}")
        print(f"   Verified names count: {len(test_names)}")
        
        unique_test_names = set(test_names)
        print(f"   Verified unique names: {len(unique_test_names)}")
        
        for name in sorted(unique_test_names):
            count = list(test_names).count(name)
            print(f"     • {name} ({count} embeddings)")
        
    else:
        print(f"\n⚠️  No database persons found in facebank!")
        print(f"   Creating empty facebank...")
        
        # Create empty tensors
        empty_embeddings = torch.empty((0, embeddings.shape[1]), dtype=embeddings.dtype)
        empty_names = np.array([])
        
        torch.save(empty_embeddings, facebank_pth_path)
        np.save(names_npy_path, empty_names)
        
        print(f"   ✅ Created empty facebank")
    
    print(f"\n🎉 CLEANUP COMPLETED!")
    print(f"   • Database and facebank are now synchronized")
    print(f"   • Backup files saved in: {backup_dir}")
    print(f"   • System will only recognize database persons")
    
    return True

def restore_from_backup():
    """Restore facebank from backup (if needed)"""
    
    backup_dir = "./demo_server/media/alert_photos/facebank_backup"
    
    if not os.path.exists(backup_dir):
        print("❌ No backup directory found")
        return False
    
    # Find latest backup files
    backup_files = [f for f in os.listdir(backup_dir) if f.startswith('facebank_backup_')]
    if not backup_files:
        print("❌ No backup files found")
        return False
    
    latest_backup = sorted(backup_files)[-1]
    backup_pth = os.path.join(backup_dir, latest_backup)
    backup_npy = os.path.join(backup_dir, latest_backup.replace('facebank_backup_', 'names_backup_'))
    
    facebank_dir = "./demo_server/media/alert_photos/facebank"
    facebank_pth_path = os.path.join(facebank_dir, "facebank.pth")
    names_npy_path = os.path.join(facebank_dir, "names.npy")
    
    print(f"🔄 Restoring from backup: {latest_backup}")
    shutil.copy2(backup_pth, facebank_pth_path)
    shutil.copy2(backup_npy, names_npy_path)
    
    print(f"✅ Facebank restored from backup")
    return True

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_from_backup()
    else:
        create_clean_facebank()
        print(f"\n💡 To restore original facebank, run:")
        print(f"   python test_cleanup_facebank.py restore")
