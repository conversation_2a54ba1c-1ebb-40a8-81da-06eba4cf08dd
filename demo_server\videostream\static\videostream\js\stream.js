
let frameCount = 0;
let lastFrameTime = Date.now();
let fpsUpdateInterval;
let fpsEstimate = 0;

async function startStream() {
    const streamUrl = document.getElementById('stream-url').value.trim();
    const cameraId = document.getElementById('camera-id').value.trim();
    console.log('Stream URL:', streamUrl);
    console.log('Camera ID:', cameraId);

    if (!streamUrl) {
        updateStatus('No stream URL provided');
        return;
    }

    if (!cameraId) {
        updateStatus('Camera ID is required');
        return;
    }

    try {
        // Check if we're on the zone creator page to disable face recognition
        const isZoneCreator = window.location.pathname.includes('/zone_creator/');
        console.log('STARTSTREAM_JS: Current URL:', window.location.pathname);
        console.log('STARTSTREAM_JS: Is Zone Creator page:', isZoneCreator);
        console.log('STARTSTREAM_JS: disable_face_recognition will be set to:', isZoneCreator);
        
        updateStatus('Connecting to stream...');
        const response = await fetch('/videostream/start/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                url: streamUrl,
                camera_id: cameraId,
                disable_face_recognition: isZoneCreator
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Server responded with ${response.status}`);
        }

        const streamImg = document.getElementById('stream');
        // Add a timestamp and camera_id to prevent caching and specify the camera
        streamImg.src = `/videostream/stream/?camera_id=${cameraId}&t=${new Date().getTime()}`;
        streamImg.onerror = () => {
            updateStatus('Failed to load stream. Check if the camera is accessible.');
            isStreamActive = false;
        };

        // Mark stream as active
        isStreamActive = true;
        updateStatus('Connected to local stream');
        startFPSCounter();


        updateDebugInfo('Streaming from: ' + streamUrl);
        
        // Stream başarıyla başlatıldıktan sonra zone filtresini sadece Stream Viewer sayfasında etkinleştir
        const isStreamViewer = window.location.pathname.includes('/viewer/');
        console.log('STREAM_JS: Current URL:', window.location.pathname);
        console.log('STREAM_JS: Is Stream Viewer:', isStreamViewer);
        if (isStreamViewer) {
            console.log('STREAM_JS: Activating zone filter in 2 seconds');
            setTimeout(() => {
                activateZoneFilter(cameraId);
            }, 2000); // Stream başlaması için kısa bir süre bekle
        } else {
            console.log('STREAM_JS: NOT Stream Viewer, skipping zone filter activation');
        }
        
    } catch (e) {
        isStreamActive = false;
        updateStatus(`Error: ${e.message}`);
        updateDebugInfo(e.stack || e.toString());
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

async function stopStream() {
    try {
        const streamUrl = document.getElementById('stream-url').value.trim();
        const cameraId = document.getElementById('camera-id').value.trim();
        const url = new URL(streamUrl);

        await fetch('/videostream/stop/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                camera_id: cameraId
            })
        });


        // <img> elementini tamamen kaldır ve tekrar ekle
        const streamImg = document.getElementById('stream');
        streamImg.src = '';
        const parent = streamImg.parentNode;
        parent.removeChild(streamImg);
        const newImg = document.createElement('img');
        newImg.id = 'stream';
        newImg.className = 'stream-img';
        newImg.alt = 'Stream';
        parent.insertBefore(newImg, parent.firstChild);

        // Mark stream as inactive
        isStreamActive = false;
        updateStatus('Stream stopped');
        stopFPSCounter();
    } catch (e) {
        isStreamActive = false;
        updateStatus(`Error stopping stream: ${e.message}`);
    }
}

function updateStatus(message) {
    document.getElementById('status').textContent = message;
}

function updateDebugInfo(info) {
    document.getElementById('debug-info').textContent =
        typeof info === 'object' ? JSON.stringify(info, null, 2) : info;
}

function startFPSCounter() {
    frameCount = 0;
    lastFrameTime = Date.now();
    clearInterval(fpsUpdateInterval);

    // Use requestAnimationFrame for more accurate frame counting
    const streamImg = document.getElementById('stream');

    // Create a hidden canvas to detect new frames
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    let lastImageData = null;

    function checkForNewFrame() {
        if (streamImg.complete && streamImg.naturalHeight !== 0) {
            canvas.width = streamImg.naturalWidth;
            canvas.height = streamImg.naturalHeight;

            try {
                ctx.drawImage(streamImg, 0, 0);
                const imageData = ctx.getImageData(0, 0, 10, 10); // Sample a small portion

                // Compare with previous frame
                if (lastImageData) {
                    let isDifferent = false;
                    for (let i = 0; i < imageData.data.length; i += 20) { // Sample every 20 pixels
                        if (imageData.data[i] !== lastImageData.data[i]) {
                            isDifferent = true;
                            break;
                        }
                    }
                    if (isDifferent) {
                        frameCount++;
                    }
                }
                lastImageData = imageData;
            } catch (e) {
                console.log("Error analyzing frame:", e);
            }
        }

        requestAnimationFrame(checkForNewFrame);
    }

    // Start the animation frame loop
    requestAnimationFrame(checkForNewFrame);

    // Update the display every second
    fpsUpdateInterval = setInterval(updateFPS, 1000);
}

function stopFPSCounter() {
    clearInterval(fpsUpdateInterval);
    document.getElementById('frame-counter').textContent = '0 FPS';
}

function updateFPS() {
    const currentTime = Date.now();
    const elapsed = (currentTime - lastFrameTime) / 1000;

    // Calculate FPS and apply smoothing
    const rawFps = frameCount / elapsed;
    fpsEstimate = Math.round(fpsEstimate * 0.7 + rawFps * 0.3); // Smoothing

    const fpsElement = document.getElementById('frame-counter');
    fpsElement.textContent = `${fpsEstimate} FPS`;
    fpsElement.style.display = 'block';

    // Reset counter
    frameCount = 0;
    lastFrameTime = currentTime;

    // Update the server about the camera's actual FPS if it's a local stream
    const cameraId = document.getElementById('camera-id').value.trim();
    if (cameraId) {
        fetch('/videostream/update_fps/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                camera_id: cameraId,
                fps: fpsEstimate
            })
        }).catch(error => console.log("Error updating FPS on server:", error));
    }
}

// Zone filtresini etkinleştiren fonksiyon
async function activateZoneFilter(cameraId) {
    console.log("ACTIVATE_ZONE_FILTER: Function called with camera_id:", cameraId);
    if (!cameraId) {
        console.error("ACTIVATE_ZONE_FILTER: Camera ID is missing");
        return;
    }
    
    try {
        console.log("ACTIVATE_ZONE_FILTER: Starting zone filter activation...");
        
        // Toggle areas kontrolünü de güncelle (eğer varsa)
        const toggleAreas = document.getElementById('toggle-areas');
        if (toggleAreas) {
            toggleAreas.checked = true;
            console.log("ACTIVATE_ZONE_FILTER: toggle-areas checkbox found and checked");
        } else {
            console.log("ACTIVATE_ZONE_FILTER: toggle-areas checkbox NOT found");
        }
        
        console.log("ACTIVATE_ZONE_FILTER: Sending POST request to /videostream/toggle_zone_filter/");
        const response = await fetch('/videostream/toggle_zone_filter/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                camera_id: cameraId,
                filter_active: true // Her zaman true gönder
            })
        });
        
        console.log("ACTIVATE_ZONE_FILTER: Received response, status:", response.status);
        const result = await response.json();
        console.log("ACTIVATE_ZONE_FILTER: Response data:", result);
        
        if (!response.ok) {
            console.error(`ACTIVATE_ZONE_FILTER: Error enabling zone filter: ${result.error}`);
        } else {
            console.log("ACTIVATE_ZONE_FILTER: Zone filter activated successfully on stream start");
            updateDebugInfo("Zone filter activated");
        }
    } catch (error) {
        console.error("ACTIVATE_ZONE_FILTER: Error activating zone filter:", error);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const streamUrl = document.getElementById('stream-url').value;
});

// Track if stream is currently active
let isStreamActive = false;

// Only stop stream on page unload if stream is actually running
window.addEventListener('beforeunload', function(event) {

    // Try to stop stream gracefully without showing confirmation dialog
    try {
        // Use sendBeacon for more reliable cleanup on page unload
        const cameraId = document.getElementById('camera-id').value.trim();
        const streamUrl = document.getElementById('stream-url').value.trim();

        if (cameraId && streamUrl) {
            const url = new URL(streamUrl);
            const isLocalStream = true;

            if (isLocalStream) {
                // Use sendBeacon for reliable cleanup
                const data = JSON.stringify({ camera_id: cameraId });
                navigator.sendBeacon('/videostream/stop/', data);
            }
        }
    } catch (e) {
        console.error('Error during cleanup:', e);
    }

    // Mark as inactive
    isStreamActive = false;

});