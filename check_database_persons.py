#!/usr/bin/env python3
"""
Simple script to check database persons and demonstrate the sync issue
"""

import os
import sys
import django

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
sys.path.append('./demo_server')
django.setup()

from alerts.models import <PERSON><PERSON><PERSON>erson, AlertPhoto

def check_database_persons():
    """Check what persons exist in the database"""
    
    print("=" * 60)
    print("DATABASE PERSONS CHECK")
    print("=" * 60)
    
    # Get all persons from database
    persons = AlertPerson.objects.all()
    
    print(f"📊 Total persons in database: {persons.count()}")
    
    if persons.exists():
        print(f"\n👥 Persons in database:")
        for i, person in enumerate(persons, 1):
            photo_count = AlertPhoto.objects.filter(person=person).count()
            print(f"   {i:2d}. {person.name} ({'Unknown' if person.is_unknown else 'Known'}) - {photo_count} photos")
    else:
        print("   No persons found in database")
    
    # Show some statistics
    known_count = persons.filter(is_unknown=False).count()
    unknown_count = persons.filter(is_unknown=True).count()
    
    print(f"\n📈 Statistics:")
    print(f"   • Known persons: {known_count}")
    print(f"   • Unknown persons: {unknown_count}")
    print(f"   • Total photos: {AlertPhoto.objects.count()}")

if __name__ == "__main__":
    check_database_persons()
