# New multiprocessing manager
import multiprocessing as mp
from multiprocessing import Process, Queue, shared_memory

# Import multiprocessing utilities for CUDA compatibility
from web_server.mp_utils import setup_multiprocessing

import logging
from typing import Optional
import time
from .process_worker import camera_worker_process
import threading
from videostream.managers.commands import <PERSON>Command, CameraResult, CameraStatus


class CameraProcessInfo:
    """Class that holds all information for a camera process"""
    def __init__(self, camera_id: int, stream_url: str, stream_queue: Queue, result_queue: Queue, command_queue: Queue):
        self.camera_id = camera_id
        self.stream_url = stream_url
        self.process: Optional[Process] = None
        self.stream_queue = stream_queue
        self.result_queue = result_queue
        self.command_queue = command_queue
        self.status: CameraStatus = CameraStatus.RUNNING
        self.last_heartbeat: Optional[float] = None
        self.error_message: Optional[str] = None
        self.model_on: bool = True  # Model processing enabled/disabled
        self.shutdown_event = mp.Event()

    def is_alive(self) -> bool:
        """Check if the process is alive"""
        return self.process is not None and self.process.is_alive()
    
    def send_command(self, command: str, data: dict = None):
        """Send command to the process"""
        print(f"CAMERA_PROCESS_INFO.send_command: camera_id={self.camera_id}, command={command}, data={data}")
        if self.command_queue:
            command_data = {
                "command": command,
                "data": data or {},
                "timestamp": time.time()
            }
            print(f"CAMERA_PROCESS_INFO.send_command: Putting command_data in queue: {command_data}")
            self.command_queue.put(command_data)
            print(f"CAMERA_PROCESS_INFO.send_command: Command sent successfully")
        else:
            print(f"CAMERA_PROCESS_INFO.send_command: No command queue available")
    
    def get_result(self, timeout: float = 0.1):
        """Get result from the process"""
        if self.result_queue:
            try:
                return self.result_queue.get(timeout=timeout)
            except:
                return None
        return None
    
    def update_heartbeat(self):
        """Update the last heartbeat timestamp"""
        self.last_heartbeat = time.time()

    def cleanup(self):
        """Clean up process resources"""
        # Stop the process
        if self.process and self.process.is_alive():
            self.process.terminate()
            self.process.join(timeout=5.0)

        # Clean up queues
        try:
            if self.result_queue:
                while not self.result_queue.empty():
                    self.result_queue.get_nowait()
                self.result_queue.close()
                self.result_queue = None
            
            if self.command_queue:
                while not self.command_queue.empty():
                    self.command_queue.get_nowait()
                self.command_queue.close()
                self.command_queue = None
                self.shutdown_event.set()  # Signal shutdown to worker process
        except Exception as e:
            logging.error(f"Error cleaning queues for camera {self.camera_id}: {e}")

        self.status = CameraStatus.STOPPED

class ProcessCameraManager:
    def __init__(self):
        self.cameras = {}  # camera_id -> CameraProcessInfo
        self.global_command_queue = Queue()  # For broadcast commands to all cameras


        # For background monitoring
        self._monitoring = False
        self._monitor_thread = None
        self._monitor_interval = 10  # Check every 10 seconds

    def check_heartbeats(self, timeout_seconds: int = 60):
        """Check for heartbeat timeouts"""
        current_time = time.time()
        timeout_cameras = []
        
        for camera_id, camera_info in self.cameras.items():
            if (camera_info.last_heartbeat and 
                current_time - camera_info.last_heartbeat > timeout_seconds):
                timeout_cameras.append(camera_id)
                logging.warning(f"Camera {camera_id} heartbeat timeout")
        
        for camera_id in timeout_cameras:
            self.cleanup_terminated_camera(camera_id)
        
        return len(timeout_cameras)
            
    def start_monitoring(self):
        """Start background monitoring"""
        if not self._monitoring:
            self._monitoring = True
            self._monitor_thread = threading.Thread(
                target=self._background_monitor,
                daemon=True,  # Thread will exit when the main program does
                name="CameraProcessMonitor"
            )
            self._monitor_thread.start()
            logging.info("Camera process monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=2.0)
            logging.info("Camera process monitoring stopped")
    
    def _background_monitor(self):
        """Monitoring function running in background thread"""
        while self._monitoring:
            try:
                # Dead process check
                dead_count = self.check_dead_processes()
                if dead_count > 0:
                    logging.info(f"Cleaned up {dead_count} dead processes")

                # Heartbeat check (optional)
                timeout_count = self.check_heartbeats(timeout_seconds=30)
                if timeout_count > 0:
                    logging.info(f"Cleaned up {timeout_count} timeout processes")

                # Periodic result cleanup
                self.get_all_results()
                
            except Exception as e:
                logging.error(f"Error in background monitor: {e}")

            # Wait for the specified interval
            time.sleep(self._monitor_interval)


    def get_camera(self, camera_id: int) -> Optional[CameraProcessInfo]:
        """Get camera information"""
        return self.cameras.get(camera_id)
    
    def start_camera_process(self, camera_id: int, stream_url: str, camera_obj):
        """Start camera process"""
        # If already exists, stop it
        if camera_id in self.cameras:
            self.stop_camera_process(camera_id)

        # Create new camera info
        camera_info = CameraProcessInfo(
            camera_id=camera_id,
            stream_url=stream_url,
            stream_queue=Queue(maxsize=50),  # Frame stream queue
            result_queue=Queue(maxsize=50),  # Frame results queue
            command_queue=Queue(maxsize=20),  # Commands queue
        )

        camera_info.process = Process(
            target=camera_worker_process,
            args=(
                camera_id,
                stream_url,
                camera_info.stream_queue,
                camera_info.result_queue,
                camera_info.command_queue,
                camera_info.shutdown_event,
                camera_obj
            )
        )

        try:
            camera_info.process.start()
            self.cameras[camera_id] = camera_info
            logging.info(f"Camera {camera_id} process started successfully")
        except Exception as e:
            logging.error(f"Failed to start camera {camera_id} process: {e}")
            camera_info.status = CameraStatus.ERROR
            camera_info.error_message = str(e)
            # Cleanup if start failed
            if camera_info.process:
                camera_info.process.terminate()
                logging.info(f"Camera {camera_id} process terminated due to start failure")
        
    def stop_camera_process(self, camera_id: int):
        """Stop camera process safely"""
        if camera_id in self.cameras:
            camera_info = self.cameras[camera_id]
            camera_info.shutdown_event.set()  # Signal the worker to stop
            camera_info.cleanup()

            # Update database to reflect that camera is no longer streaming
            self._update_camera_db_status(camera_id, False)

            if camera_id in self.cameras: del self.cameras[camera_id]
            logging.info(f"Camera {camera_id} process stopped")
    
    def send_command_to_camera(self, camera_id: int, command: str, data: dict = None):
        """Send command to specific camera"""
        print(f"SEND_COMMAND_TO_CAMERA: camera_id={camera_id}, command={command}, data={data}")
        camera = self.get_camera(camera_id)
        if camera:
            print(f"SEND_COMMAND_TO_CAMERA: Camera found, sending command")
            camera.send_command(command, data)
            return True
        else:
            print(f"SEND_COMMAND_TO_CAMERA: Camera NOT found")
        return False
    
    def broadcast_command(self, command: str, data: dict = None):
        """Send command to all cameras"""
        for camera_info in self.cameras.values():
            camera_info.send_command(command, data)
    
    def get_camera_result(self, camera_id: int):
        """Get result from camera"""
        camera = self.get_camera(camera_id)
        if camera:
            return camera.get_result()
        return None
    
    def get_all_results(self):
        """Get all results from cameras and check process statuses"""
        results = {}
        terminated_cameras = []
        
        for camera_id, camera_info in self.cameras.items():
            result = camera_info.get_result()
            if result:
                results[camera_id] = result

                # Check for process termination message
                if result.get('type') == CameraResult.PROCESS_TERMINATED:
                    terminated_cameras.append(camera_id)
                    logging.warning(f"Camera {camera_id} process terminated: {result.get('reason', 'unknown')}")

                # Check for error messages
                elif result.get('type') == CameraResult.ERROR:
                    camera_info.error_message = result.get('message', 'Unknown error')
                    logging.error(f"Camera {camera_id} error: {camera_info.error_message}")

        # Clean up terminated processes
        for camera_id in terminated_cameras:
            self.cleanup_terminated_camera(camera_id)
            
        return results
    
    def get_camera_status(self, camera_id: int):
        """Get camera status"""
        camera = self.get_camera(camera_id)
        if camera:
            return {
                "status": camera.status,
                "is_alive": camera.is_alive(),
                "last_heartbeat": camera.last_heartbeat,
                "error_message": camera.error_message
            }
        return None
    
    def get_all_camera_status(self):
        """Get all camera statuses"""
        status = {}
        for camera_id, camera_info in self.cameras.items():
            status[camera_id] = {
                "status": camera_info.status,
                "is_alive": camera_info.is_alive(),
                "last_heartbeat": camera_info.last_heartbeat,
                "error_message": camera_info.error_message
            }
        return status
    
    def cleanup_all(self):
        """Clean up all processes"""
        for camera_id in list(self.cameras.keys()):
            self.stop_camera_process(camera_id)
    
    def toggle_model(self, camera_id: int, model_on: bool):
        """Toggle model processing for specific camera"""
        camera = self.get_camera(camera_id)
        if camera:
            camera.model_on = model_on
            # Send command to process
            camera.send_command("toggle_model", {"model_on": model_on})
            logging.info(f"Camera {camera_id} model {'enabled' if model_on else 'disabled'}")
            return True
        return False

    def toggle_all_models(self, model_on: bool):
        """Toggle model processing for all cameras"""
        for camera_id, camera_info in self.cameras.items():
            camera_info.model_on = model_on
            camera_info.send_command(CameraCommand.TOGGLE_MODEL, {"model_on": model_on})
        logging.info(f"All cameras model {'enabled' if model_on else 'disabled'}")
    
    def get_frame(self, camera_id: int):
        """Get frame from camera - Return the most pending (first) frame"""
        camera = self.get_camera(camera_id)
        if not camera:
            #print("Camera is not avaliable!!!!")
            return None

        # Get the first frame from the result queue
        try:
            if camera.result_queue is None:
                print(f"Camera {camera_id} result queue is None")
                return None

            if camera.result_queue.empty():
                # print(f"No frame available for camera {camera_id}")
                return None

            result = camera.result_queue.get_nowait()
            print(f"Received result for camera {camera_id}: {result['type']}")
            if result['type'] == CameraResult.FRAME:
                # Use the timestamp in the frame message as the heartbeat
                camera.last_heartbeat = result['capture_time']
                print(f"Received frame for camera {camera_id} at {result['capture_time']}")
                return result['frame_data']
            elif result['type'] == CameraResult.ERROR:
                camera.error_message = result.get('message', 'Unknown error')
                print(f"Error frame for camera {camera_id}: {camera.error_message}")
                return None
            elif result['type'] == CameraResult.PROCESS_TERMINATED:
                # Process terminated, perform cleanup
                logging.warning(f"Camera {camera_id} process terminated during get_frame")
                self.cleanup_terminated_camera(camera_id)
                return None
            else:
                # Ignore other message types (metrics, status, etc.)
                # Will be retried in the next call
                print(f"Received non-frame message for camera {camera_id}: {result['type']}")
                return None
        except Exception as e:
            print(f"ERROR: Unexpected error while getting frame for camera {camera_id}: {e}")
            return None
            


    def cleanup_terminated_camera(self, camera_id: int):
        """Clean up terminated camera"""
        if camera_id in self.cameras:
            camera_info = self.cameras[camera_id]

            # Process should already be terminated, but check just in case
            if camera_info.process and camera_info.process.is_alive():
                logging.warning(f"Camera {camera_id} process still alive during cleanup, terminating...")
                camera_info.process.terminate()
                camera_info.process.join(timeout=2.0)

            # Clean up queues
            camera_info.cleanup()

            # Update database to reflect that camera is no longer streaming
            self._update_camera_db_status(camera_id, False)

            # Remove camera info
            if camera_id in self.cameras: del self.cameras[camera_id]
            logging.info(f"Terminated camera {camera_id} cleaned up")

    def check_dead_processes(self):
        """Check for dead processes and clean up"""
        dead_cameras = []
        
        for camera_id, camera_info in self.cameras.items():
            if not camera_info.is_alive():
                dead_cameras.append(camera_id)
                logging.warning(f"Dead process detected for camera {camera_id}")
        
        for camera_id in dead_cameras:
            self.cleanup_terminated_camera(camera_id)
        
        return len(dead_cameras)

    def _update_camera_db_status(self, camera_id: int, is_streaming: bool):
        """Update camera streaming status in database"""
        try:
            # Import here to avoid circular imports
            from cameras.models import Camera
            camera = Camera.objects.get(id=camera_id)
            camera.is_streaming = is_streaming
            camera.save()
            logging.info(f"Updated camera {camera_id} DB status to is_streaming={is_streaming}")
        except Exception as e:
            logging.error(f"Error updating camera {camera_id} DB status: {e}")