import torch
import os

facebank_path = "./demo_server/media/alert_photos/facebank.pth"  # kendi pathini koy buraya

# .pth dosyasını yükle
data = torch.load(facebank_path, map_location="cpu")

print("facebank.pth tipi:", type(data))

# <PERSON><PERSON><PERSON> dict ise key'leri ya<PERSON>
if isinstance(data, dict):
    print("Keys:", data.keys())
    for k, v in data.items():
        print(f"{k}: type={type(v)} shape={getattr(v, 'shape', None)}")
elif isinstance(data, list):
    print("List length:", len(data))
    for i, v in enumerate(data):
        print(f"[{i}] type={type(v)} shape={getattr(v, 'shape', None)}")
else:
    print("İçerik:", data)
